# ###################################################################
# Mp3tag Tag Source for VocaDB
# Original (usually Japanese) tags source
#
# Search by ALBUM
#
# This file should be stored in your tag sources sources directory
# %APPDATA%\Mp3tag\data\sources
# and requires Mp3tag v2.64 or above.
#
# [2022-01-19] v1.20
# Created VocaDB#Original &Tags.src from VocaDB.src v1.13.
# Differences from VocaDB#Romaji Tags: 
# - Album search shows Default (usually Japanese) names
# - All names and titles will all be in the original language of the song.
# - lyrics will be in the original language
#
# [2022-01-19] v1.21
# Add month and day to year tag
#
# [2022-03-23] v1.22
# Now adds " (Instrumental)" to Instrumental song titles.
# 
# (further updates will be listed on the git commit log: https://github.com/Onurtag/Mp3tag-Sources )
#
# ###################################################################

[Name]=VocaDB
[BasedOn]=vocadb.net
[IndexUrl]=https://vocadb.net/api/albums?fields=AdditionalNames,Tags,Tracks,Names,MainPicture&nameMatchMode=Auto&maxResults=50&preferAccurateMatches=true&getTotalCount=true&lang=Default&query=%s
[AlbumUrl]=https://vocadb.net/api/albums/
[WordSeparator]=%20
[IndexFormat]=%Artist%|%Album%|%Release_Date%|%Catalog_Number%|%_preview%|%_url%|%tracks%
[SearchBy]=%album%
[UserAgent]=1
[Encoding]=url-utf-8

[ParserScriptIndex]=...
# ###################################################################
#					I  N  D  E  X
# ###################################################################

# Debug "on" "D:\\KeepThisEmpty\\vocadb_orig_DEBUG.json"
# DebugWriteInput "D:\\KeepThisEmpty\\vocadb_orig.json"

# replace problematic characters for Mp3tag
replace "|" "$verticalBar()"
# use "current" json (applies replacements above)
json "ON" "current"

json_select "totalCount"

ifnot "0"
	json_foreach "items"
		
		# Artist
		json_select "artistString"
		#sayregexp ".+?(?= -.*)"
		sayrest
		say "|" 

		# Album
		json_select "defaultName"
		#sayregexp "(?<= - ).*"
		sayrest
		say "|"

		# ReleaseDate
		json_select_object "releaseDate"
			json_select "formatted"
			sayrest
			say "|"
		json_unselect_object

		# Catalog_Number
		json_select "catalogNumber"
		sayrest
		say "|"


		# Preview
		say "https://www.vocadb.net/Al/"
		json_select "id"
		sayrest
		say "|"

		# URL
		json_select "id"
		sayrest
		say "?fields=Tracks,Names&lang=Default&songFields=Lyrics"
		say "|"

		# Tracks
		json_foreach "tracks"
			set "t_tracks"
			json_select "trackNumber"
			outputto "t_tracks"
		json_foreach_end
		outputto "Output"
		sayrest
		saynewline

	json_foreach_end
endif

[ParserScriptAlbum]=...
# ###################################################################
#					A  L  B  U  M
# ###################################################################

# Debug "on" "D:\\KeepThisEmpty\\vocadb_orig_single_DEBUG.json"
# DebugWriteInput "D:\\KeepThisEmpty\\vocadb_orig_single.json"

# replace problematic characters for Mp3tag
replace "|" "$verticalBar()"
# use "current" json (applies replacements above)
json "ON" "current"

json_select_object "items"

	# Album
	outputto "ALBUM"
	json_select "name"
	sayrest

	# # Default Album Name (JP) into JAPANESEALBUM tag
	# outputto "JAPANESEALBUM"
	# json_select "defaultName"
	# sayrest

	# VOCADB_ID
	outputto "VOCADB_ID"
	json_select "id"
	sayrest

	# Coverurl
	outputto "coverurl"
	json_select "id"
	say "https://vocadb.net/Album/CoverPicture/"
	sayrest

	# Catalog Number
	outputto "CATALOGID"
	json_select "catalogNumber"
	#json_select_many "labels" "catno" ", "
	sayrest

	# Publisher
	outputto "PUBLISHER"
	json_select_many "labels" "name" ", "
	sayrest

	# Media Type
	outputto "MEDIATYPE"
	json_select "discType"
	sayrest

	# Year
	outputto "YEAR"
	json_select_object "releaseDate"
		json_select "year"
		sayrest
		say "."
		json_select "month"
		sayrest
		say "."
		json_select "day"
		sayrest
	json_unselect_object

	outputto "ALBUMARTIST"
	json_select "artistString"
	sayrest

	# Names and sortnames
	json_foreach "names"
		json_select "language"
		if "English"
			outputto "ALBUMSORT"
			json_select "value"
			sayrest
		endif
	json_foreach_end

	# Tracks
	json_foreach "tracks"
		outputto "DISCNUMBER"
		json_select "discNumber"
		sayrest
		say "|"

		outputto "TRACK"
		json_select "trackNumber"
		sayrest
		say "|"

		outputto "TRACKS"
		json_select "name"
		sayrest
		# # Don't say | here because we do it after (Instrumental)
		# say "|"

		json_select_object "song"
			# VOCADB_TRACK_ID
			outputto "VOCADB_TRACK_ID"
			json_select "id"
			sayrest
			say "|"

			outputto "_LENGTH"
			json_select "lengthSeconds"
			sayrest
			say "|"

			outputto "Artist"
			json_select "artistString"
			sayrest
			say "|"

			# Append " (Instrumental)" to the title of instrumental tracks
			outputto "TRACKS"
			json_select "songType"
			if "Instrumental"
				say " (Instrumental)"
			endif
			say "|"

			# # Default Title (JP) into JAPANESETITLE tag
			# outputto "JAPANESETITLE"
			# json_select "defaultName"
			# sayrest
			# say "|"

			# Original Lyrics into UNSYNCEDLYRICS tag
			#START lyrics block
			outputto "UNSYNCEDLYRICS"
			json_foreach "lyrics"
				ifnot "0"
					#parse lyric types for translationType: "Original"
					json_select "translationType"
					if "Original"
						json_select "value"
						sayrest
					endif
				endif
			json_foreach_end
			say "|"
			#END lyrics block

		json_unselect_object
	json_foreach_end

	set "tmp_join"

	###
	# VocaDB specific tags
	###

	# Release event
	outputto "VOCADB_RELEASE_EVENT"
	json_select "releaseEvent"
	sayrest
json_unselect_object