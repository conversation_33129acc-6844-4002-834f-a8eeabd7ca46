# ###################################################################
# Mp3tag Tag Source for VocaDB
# Romaji tags source
#
# Search using SONG ID
#
# This file should be stored in your tag sources sources directory
# %APPDATA%\Mp3tag\data\sources
# and requires Mp3tag v2.64 or above.
#
# ###################################################################

[Name]=VocaDB
[BasedOn]=vocadb.net
[AlbumUrl]=https://vocadb.net/api/songs/%s?fields=AdditionalNames,Lyrics,MainPicture&lang=Romaji
[WordSeparator]=%20
[SearchBy]=Enter Song ID||%VOCADB_ID%||%s
[UserAgent]=1
[Encoding]=url-utf-8

[ParserScriptAlbum]=...
# ###################################################################
#					S  O  N  G
# ###################################################################

#Debug "on" "D:\\KeepThisEmpty\\vocadb_romaji_songid_DEBUG.json"
#DebugWriteInput "D:\\KeepThisEmpty\\vocadb_romaji_songid.json"

# replace problematic characters for Mp3tag
replace "|" "$verticalBar()"
# use "current" json (applies replacements above)
json "ON" "current"

# For song ID queries, the API returns a single song object directly, not wrapped in "items"
# Extract the song data directly from the root object

	# Song Title
	outputto "TITLE"
	json_select "name"
	sayrest

	# Default Song Name (JP) into JAPANESETITLE tag
	outputto "JAPANESETITLE"
	json_select "defaultName"
	sayrest

	# VOCADB_TRACK_ID
	outputto "VOCADB_TRACK_ID"
	json_select "id"
	sayrest

	# Song Length
	outputto "_LENGTH"
	json_select "lengthSeconds"
	sayrest

	# Artist
	outputto "ARTIST"
	json_select "artistString"
	sayrest

	# Append " (Instrumental)" to the title of instrumental tracks
	outputto "TITLE"
	json_select "songType"
	if "Instrumental"
		say " (Instrumental)"
	endif

	# Song Type
	outputto "VOCADB_TRACK_TYPE"
	json_select "songType"
	sayrest

	# Romanized Lyrics into UNSYNCEDLYRICS tag
	#START lyrics block
	outputto "UNSYNCEDLYRICS"
	json_foreach "lyrics"
		ifnot "0"
			#parse lyric types for translationType: "Romanized"
			json_select "translationType"
			if "Romanized"
				json_select "value"
				sayrest
			endif
		endif
	json_foreach_end
	#END lyrics block

	# Names and sortnames
	json_foreach "names"
		json_select "language"
		if "English"
			outputto "TITLESORT"
			json_select "value"
			sayrest
		endif
	json_foreach_end

	# Cover art from main picture
	outputto "coverurl"
	json_select_object "mainPicture"
		json_select "urlOriginal"
		sayrest
	json_unselect_object

	# Dates
	outputto "VOCADB_TRACK_PUBLISHDATE"
	json_select "publishDate"
	sayrest

	outputto "VOCADB_TRACK_CREATEDATE"
	json_select "createDate"
	sayrest

