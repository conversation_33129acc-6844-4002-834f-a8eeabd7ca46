
<!-- TODO: use ifNotOutput "TEMP_Title" method like the vgmdb json source -->



## Installation  
Requires Mp3tag v2.64 or above.  
.src files should be directly in your tag sources directory: **%APPDATA%\Mp3tag\data\sources**  
(like C:\Users\<USER>\Appdata\Roaming\Mp3tag\data\sources)  

You can download all .src files using the **Code -> Download ZIP** button.  

## VocaDB Sources  


- #### VocaDB Romaji Tags.src:   
  - Updated version of the old VocaDB.src source.   
  - All names, titles and lyrics will be in Romaji if they exist.   
  - JAPANESETITLE and JAPANESEALBUM tags will be added to each song.  
  - Appends " (Instrumental)" to instrumental track names

- #### VocaDB Original &Tags.src:  
  Created VocaDB#Original &Tags.src from VocaDB.src v1.13.  
  
  Differences between VocaDB#Romaji Tags.src: 
  - Album search shows Default (usually Japanese) names  
  - All names and titles will all be in the original language of the song.  
  - lyrics will be in the original language (usually Japanese)  

- #### VocaDB Album ID Romaji Tags.src:  
  - Search using VocaDB Album ID (album number on the URL)  

- #### VocaDB Album ID Original Tags.src:  
  - Search using VocaDB Album ID (album number on the URL)  

- #### VocaDB Song ID Romaji Tags.src:  
  - Search using VocaDB Song ID (song number on the URL)  

- #### VocaDB Song ID Original Tags.src:  
  - Search using VocaDB Song ID (song number on the URL)  



&nbsp;

## TouhouDB and UtaiteDB Sources  
They are exactly the same as VocaDB sources.  

&nbsp;
---

- #### Many thanks to the author of VocaDB.src **PBX_g33k** from [mp3tag forums](https://community.mp3tag.de/t/ws-vocadb-net/17192).
