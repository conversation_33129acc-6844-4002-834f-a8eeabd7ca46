# ###################################################################
# 
# UtaiteDB version of "VocaDB Album ID Original Tags.src"
#
# This file should be stored in your tag sources sources directory
# %APPDATA%\Mp3tag\data\sources
# and requires Mp3tag v2.64 or above.
#
# ###################################################################

[Name]=UtaiteDB
[BasedOn]=UtaiteDB.net
[AlbumUrl]=https://UtaiteDB.net/api/albums/%s?fields=AdditionalNames,Tags,Tracks,Names,MainPicture&lang=Default
[WordSeparator]=%20
[SearchBy]=Enter Album ID||%UTAITEDB_ID%||%s
[UserAgent]=1
[Encoding]=url-utf-8

[ParserScriptAlbum]=...
# ###################################################################
#					A  L  B  U  M
# ###################################################################

# Debug "on" "D:\\KeepThisEmpty\\UTAITEDB_orig_single_DEBUG.json"
# DebugWriteInput "D:\\KeepThisEmpty\\UTAITEDB_orig_single.json"

# replace problematic characters for Mp3tag
replace "|" "$verticalBar()"
# use "current" json (applies replacements above)
json "ON" "current"

json_select_object "items"

	# Album
	outputto "ALBUM"
	json_select "name"
	sayrest

	# # Default Album Name (JP) into JAPANESEALBUM tag
	# outputto "JAPANESEALBUM"
	# json_select "defaultName"
	# sayrest

	# UTAITEDB_ID
	outputto "UTAITEDB_ID"
	json_select "id"
	sayrest

	# Coverurl
	outputto "coverurl"
	json_select "id"
	say "https://UtaiteDB.net/Album/CoverPicture/"
	sayrest

	# Catalog Number
	outputto "CATALOGID"
	json_select "catalogNumber"
	#json_select_many "labels" "catno" ", "
	sayrest

	# Publisher
	outputto "PUBLISHER"
	json_select_many "labels" "name" ", "
	sayrest

	# Media Type
	outputto "MEDIATYPE"
	json_select "discType"
	sayrest

	# Year
	outputto "YEAR"
	json_select_object "releaseDate"
		json_select "year"
		sayrest
		say "."
		json_select "month"
		sayrest
		say "."
		json_select "day"
		sayrest
	json_unselect_object

	outputto "ALBUMARTIST"
	json_select "artistString"
	sayrest

	# Names and sortnames
	json_foreach "names"
		json_select "language"
		if "English"
			outputto "ALBUMSORT"
			json_select "value"
			sayrest
		endif
	json_foreach_end

	# Tracks
	json_foreach "tracks"
		outputto "DISCNUMBER"
		json_select "discNumber"
		sayrest
		say "|"

		outputto "TRACK"
		json_select "trackNumber"
		sayrest
		say "|"

		outputto "TRACKS"
		json_select "name"
		sayrest
		# # Don't say | here because we do it after (Instrumental)
		# say "|"

		json_select_object "song"
			# UTAITEDB_TRACK_ID
			outputto "UTAITEDB_TRACK_ID"
			json_select "id"
			sayrest
			say "|"

			outputto "_LENGTH"
			json_select "lengthSeconds"
			sayrest
			say "|"

			outputto "Artist"
			json_select "artistString"
			sayrest
			say "|"

			# Append " (Instrumental)" to the title of instrumental tracks
			outputto "TRACKS"
			json_select "songType"
			if "Instrumental"
				say " (Instrumental)"
			endif
			say "|"

			# # Default Title (JP) into JAPANESETITLE tag
			# outputto "JAPANESETITLE"
			# json_select "defaultName"
			# sayrest
			# say "|"

			# Original Lyrics into UNSYNCEDLYRICS tag
			#START lyrics block
			outputto "UNSYNCEDLYRICS"
			json_foreach "lyrics"
				ifnot "0"
					#parse lyric types for translationType: "Original"
					json_select "translationType"
					if "Original"
						json_select "value"
						sayrest
					endif
				endif
			json_foreach_end
			say "|"
			#END lyrics block

		json_unselect_object
	json_foreach_end

	set "tmp_join"

	###
	# UtaiteDB specific tags
	###

	# Release event
	outputto "UTAITEDB_RELEASE_EVENT"
	json_select "releaseEvent"
	sayrest
json_unselect_object